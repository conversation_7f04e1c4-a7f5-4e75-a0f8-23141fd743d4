package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/aria2"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
)

// downloadAllImages 下载所有图片（封面、演员头像等）
func downloadAllImages(movieInfo *javscraper.MovieInfo, movieCode string, stats *CompleteScrapeStats) error {
	var downloadedCount int

	// 1. 下载封面图片
	if movieInfo.CoverURL != "" {
		fmt.Printf("        🖼️  下载封面图片: %s\n", movieInfo.CoverURL)
		err := downloadCoverImage(movieInfo.CoverURL, movieCode)
		if err != nil {
			fmt.Printf("        ⚠️  封面下载失败: %v\n", err)
		} else {
			downloadedCount++
			fmt.Printf("        ✅ 封面下载成功\n")
		}
	}

	// 2. 下载海报图片
	if movieInfo.PosterURL != "" && movieInfo.PosterURL != movieInfo.CoverURL {
		fmt.Printf("        🖼️  下载海报图片: %s\n", movieInfo.PosterURL)
		err := downloadPosterImage(movieInfo.PosterURL, movieCode)
		if err != nil {
			fmt.Printf("        ⚠️  海报下载失败: %v\n", err)
		} else {
			downloadedCount++
			fmt.Printf("        ✅ 海报下载成功\n")
		}
	}

	// 3. 下载演员头像
	if len(movieInfo.Actors) > 0 {
		fmt.Printf("        👥 下载 %d 个演员头像...\n", len(movieInfo.Actors))
		actorCount := 0
		for _, actor := range movieInfo.Actors {
			if actor.AvatarURL != "" {
				err := downloadActorAvatar(actor.AvatarURL, actor.Name)
				if err != nil {
					fmt.Printf("        ⚠️  演员 %s 头像下载失败: %v\n", actor.Name, err)
				} else {
					actorCount++
				}
			}
		}
		if actorCount > 0 {
			downloadedCount += actorCount
			fmt.Printf("        ✅ 成功下载 %d 个演员头像\n", actorCount)
		}
	}

	stats.ImagesDownloaded += downloadedCount
	return nil
}

// downloadPosterImage 下载海报图片
func downloadPosterImage(posterURL, movieCode string) error {
	// 创建存储目录
	posterDir := "/www/wwwroot/JAVAPI.COM/storage/posters"
	err := os.MkdirAll(posterDir, 0755)
	if err != nil {
		return fmt.Errorf("创建海报目录失败: %w", err)
	}

	// 获取文件扩展名
	ext := filepath.Ext(posterURL)
	if ext == "" {
		ext = ".jpg"
	}

	// 构建本地文件路径
	filename := fmt.Sprintf("%s_poster%s", strings.ToLower(movieCode), ext)
	localPath := filepath.Join(posterDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		return nil // 文件已存在，跳过下载
	}

	// 使用相同的下载逻辑
	return downloadImageWithHeaders(posterURL, localPath)
}

// downloadActorAvatar 下载演员头像
func downloadActorAvatar(avatarURL, actorName string) error {
	// 创建存储目录
	actorDir := "/www/wwwroot/JAVAPI.COM/storage/actors"
	err := os.MkdirAll(actorDir, 0755)
	if err != nil {
		return fmt.Errorf("创建演员目录失败: %w", err)
	}

	// 清理演员名称作为文件名
	safeName := strings.ReplaceAll(actorName, " ", "_")
	safeName = strings.ReplaceAll(safeName, "/", "_")
	safeName = strings.ReplaceAll(safeName, "\\", "_")

	// 获取文件扩展名
	ext := filepath.Ext(avatarURL)
	if ext == "" {
		ext = ".jpg"
	}

	// 构建本地文件路径
	filename := fmt.Sprintf("%s%s", safeName, ext)
	localPath := filepath.Join(actorDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		return nil // 文件已存在，跳过下载
	}

	// 使用相同的下载逻辑
	return downloadImageWithHeaders(avatarURL, localPath)
}

// downloadImageWithHeaders 使用请求头下载图片的通用函数
func downloadImageWithHeaders(imageURL, localPath string) error {
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", imageURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	
	// 添加请求头模拟浏览器访问
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Referer", "https://www.javbus.com/")
	req.Header.Set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	return nil
}

// createAria2DownloadTask 创建真正的aria2下载任务
func createAria2DownloadTask(aria2Service service.Aria2Service, magnet *javscraper.MagnetInfo, movieCode string) error {
	if aria2Service == nil {
		return fmt.Errorf("aria2服务未连接")
	}

	// 设置下载选项（aria2容器内路径）
	options := &aria2.AddUriOptions{
		Dir:                    "/downloads",
		MaxConnections:         "16",
		Split:                  "16",
		MinSplitSize:           "1M",
		MaxConcurrentDownloads: "3",
		ContinueDownload:       "true",
		CheckIntegrity:         "true",
		AllowOverwrite:         "false",
		AutoFileRenaming:       "true",
		UserAgent:              "magnet-downloader/1.0",
	}

	// 添加磁力链接到aria2
	gid, err := aria2Service.AddMagnetTask(magnet.MagnetURL, options)
	if err != nil {
		return fmt.Errorf("添加aria2任务失败: %w", err)
	}

	logger.Infof("aria2下载任务创建成功: 影片%s, GID=%s, 磁力=%s", movieCode, gid, magnet.MagnetURL)
	return nil
}