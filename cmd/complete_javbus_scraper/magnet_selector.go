package main

import (
	"fmt"
	"regexp"
	"strings"

	"magnet-downloader/pkg/javscraper"
)

// ScoredMagnet 带评分的磁力链接
type ScoredMagnet struct {
	*javscraper.MagnetInfo
	Score float64 `json:"score"`
}

// parseSubtitleInfo 解析字幕信息
func parseSubtitleInfo(magnet *ScoredMagnet) {
	if magnet.FileName == "" {
		return
	}

	fileName := strings.ToLower(magnet.FileName)

	// 中文字幕检测
	chinesePatterns := []string{
		`(中文|中字|字幕)`,
		`\b(chs|cht|chinese)\b`,
		`(简体|繁体|简中|繁中)`,
	}

	for _, pattern := range chinesePatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			magnet.HasSubtitle = true
			magnet.SubtitleLanguage = "chinese"
			return
		}
	}

	// 英文字幕检测
	englishPatterns := []string{
		`\b(english|eng)\b`,
		`\bsub.*eng\b`,
	}

	for _, pattern := range englishPatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			magnet.HasSubtitle = true
			magnet.SubtitleLanguage = "english"
			return
		}
	}

	// 日文字幕检测
	japanesePatterns := []string{
		`\b(japanese|jpn|jp)\b`,
		`(日文|日语|日字)`,
	}

	for _, pattern := range japanesePatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			magnet.HasSubtitle = true
			magnet.SubtitleLanguage = "japanese"
			return
		}
	}

	// 默认无字幕
	magnet.HasSubtitle = false
	magnet.SubtitleLanguage = "none"
}

// parseQualityInfo 解析质量信息
func parseQualityInfo(magnet *ScoredMagnet) {
	if magnet.FileName == "" {
		return
	}

	fileName := strings.ToLower(magnet.FileName)

	qualityPatterns := map[string][]string{
		"4K":    {`\b(4k|uhd|2160p)\b`},
		"1080p": {`\b1080p?\b`},
		"720p":  {`\b720p?\b`},
		"480p":  {`\b480p?\b`},
	}

	for quality, patterns := range qualityPatterns {
		for _, pattern := range patterns {
			if matched, _ := regexp.MatchString(pattern, fileName); matched {
				magnet.Quality = quality
				return
			}
		}
	}

	magnet.Quality = "unknown"
}

// calculateMagnetScore 计算磁力链接评分
func calculateMagnetScore(magnet *ScoredMagnet) float64 {
	score := 0.0

	// 文件大小评分 (40%)
	sizeScore := calculateSizeScore(magnet.FileSize)
	score += sizeScore * 0.40

	// 字幕评分 (35%)
	subtitleScore := calculateSubtitleScore(magnet)
	score += subtitleScore * 0.35

	// 质量评分 (15%)
	qualityScore := calculateQualityScore(magnet.Quality)
	score += qualityScore * 0.15

	// 来源评分 (10%)
	sourceScore := calculateSourceScore(magnet.FileName)
	score += sourceScore * 0.10

	return score
}

// calculateSizeScore 计算文件大小评分
func calculateSizeScore(fileSize int64) float64 {
	if fileSize <= 0 {
		return 0
	}

	// 转换为GB
	sizeGB := float64(fileSize) / (1024 * 1024 * 1024)

	switch {
	case sizeGB >= 4.0:
		return 100.0 // >4GB: 100分
	case sizeGB >= 2.0:
		return 80.0 // 2-4GB: 80分
	case sizeGB >= 1.0:
		return 60.0 // 1-2GB: 60分
	case sizeGB >= 0.5:
		return 30.0 // 0.5-1GB: 30分
	default:
		return 10.0 // <0.5GB: 10分
	}
}

// calculateSubtitleScore 计算字幕评分
func calculateSubtitleScore(magnet *ScoredMagnet) float64 {
	if !magnet.HasSubtitle {
		return 0.0 // 无字幕: 0分
	}

	switch magnet.SubtitleLanguage {
	case "chinese":
		return 100.0 // 中文字幕: 100分
	case "english":
		return 60.0 // 英文字幕: 60分
	case "japanese":
		return 30.0 // 日文字幕: 30分
	default:
		return 20.0 // 其他字幕: 20分
	}
}

// calculateQualityScore 计算清晰度评分
func calculateQualityScore(quality string) float64 {
	switch quality {
	case "4K":
		return 100.0 // 4K/UHD: 100分
	case "1080p":
		return 80.0 // 1080p: 80分
	case "720p":
		return 60.0 // 720p: 60分
	case "480p":
		return 40.0 // 480p: 40分
	default:
		return 50.0 // 未知: 50分
	}
}

// calculateSourceScore 计算来源可靠性评分
func calculateSourceScore(fileName string) float64 {
	if fileName == "" {
		return 50.0
	}

	source := strings.ToLower(fileName)

	// 知名发布组列表
	knownGroups := []string{
		"fhd", "hd", "uncensored", "leaked", "1080p", "720p",
		"javhd", "javfull", "javmost", "javfree", "javdoe",
		"thz", "sukebei", "nyaa", "rarbg", "1337x",
		"carib", "caribbeancom", "1pondo", "10musume",
		"pacopacomama", "muramura", "heyzo", "fc2",
	}

	for _, group := range knownGroups {
		if strings.Contains(source, group) {
			return 100.0 // 知名发布组: 100分
		}
	}

	// 检查是否有明确的发布组标识
	if regexp.MustCompile(`\[[^\]]+\]`).MatchString(source) {
		return 70.0 // 一般来源: 70分
	}

	return 50.0 // 未知来源: 50分
}

// findBestMagnet 从磁力链接列表中找到最佳的
func findBestMagnet(magnets []*ScoredMagnet, category string) *ScoredMagnet {
	if len(magnets) == 0 {
		return nil
	}

	best := magnets[0]
	for _, magnet := range magnets[1:] {
		if magnet.Score > best.Score {
			best = magnet
		}
	}

	return best
}

// isSimilarMagnet 检查两个磁力链接是否相似（避免重复下载）
func isSimilarMagnet(magnet1, magnet2 *ScoredMagnet) bool {
	// 如果文件大小相差不超过10%，认为是相似的
	if magnet1.FileSize > 0 && magnet2.FileSize > 0 {
		diff := float64(abs(magnet1.FileSize-magnet2.FileSize)) / float64(max(magnet1.FileSize, magnet2.FileSize))
		if diff < 0.1 { // 10%以内认为相似
			return true
		}
	}

	// 如果文件名相似度很高，认为是相似的
	if magnet1.FileName != "" && magnet2.FileName != "" {
		similarity := calculateStringSimilarity(magnet1.FileName, magnet2.FileName)
		if similarity > 0.8 { // 80%以上相似度
			return true
		}
	}

	return false
}



// abs 返回绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// max 返回最大值
func max(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}

// calculateStringSimilarity 计算字符串相似度（简化版）
func calculateStringSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}
	
	s1 = strings.ToLower(s1)
	s2 = strings.ToLower(s2)
	
	// 简单的相似度计算：共同字符数 / 总字符数
	common := 0
	for _, char := range s1 {
		if strings.ContainsRune(s2, char) {
			common++
		}
	}
	
	total := len(s1) + len(s2)
	if total == 0 {
		return 0.0
	}
	
	return float64(common*2) / float64(total)
}

// formatFileSize 格式化文件大小的实际实现
func formatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return "未知大小"
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}