package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/database"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/telegram"
)

// JavBusAPI响应结构
type JavBusMoviesResponse struct {
	Movies     []JavBusMovie `json:"movies"`
	Pagination Pagination    `json:"pagination"`
}

type JavBusMovie struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Date     string `json:"date"`
	CoverURL string `json:"img"`
	Tags     []string `json:"tags"`
}

type Pagination struct {
	CurrentPage int   `json:"currentPage"`
	HasNextPage bool  `json:"hasNextPage"`
	NextPage    *int  `json:"nextPage"`
	Pages       []int `json:"pages"`
}

// 完整采集统计
type CompleteScrapeStats struct {
	TotalPages         int
	TotalMovies        int
	ProcessedMovies    int
	SuccessMovies      int
	FailedMovies       int
	NewMovies          int
	UpdatedMovies      int
	DownloadsCreated   int
	ImagesDownloaded   int
	StartTime          time.Time
	EndTime            time.Time
	Errors             []string
}

func main() {
	fmt.Println("🚀 JavBus完整采集器 (含数据库写入、图片下载、自动下载)")
	fmt.Println("=" + strings.Repeat("=", 70))

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	err = database.Init(&cfg.Database)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}

	// 数据库表已手动创建，跳过AutoMigrate
	fmt.Println("✅ 数据库连接成功")

	// 初始化服务
	repo := repository.NewRepository(database.DB)
	javService := service.NewJAVService(repo)
	aria2Service := service.NewAria2Service(&cfg.Aria2)
	telegramService := service.NewTelegramService(&cfg.Telegram)

	// 初始化JAV采集器配置
	javscraperConfig := &javscraper.Config{
		Enabled:   cfg.JAV.Enabled,
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Timeout:   30 * time.Second,
		MaxRetries: 3,
		RateLimit: 2 * time.Second,
		ExternalServices: javscraper.ExternalServicesConfig{
			JavBusAPI: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3001",
				Timeout: 30 * time.Second,
			},
			JavSPWrapper: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3002",
				Timeout: 30 * time.Second,
			},
			JavinizerWrapper: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3003",
				Timeout: 30 * time.Second,
			},
		},
		Sources: javscraper.SourcesConfig{
			JavBus: javscraper.JavBusConfig{
				Enabled:   true,
				BaseURL:   "https://www.javbus.com",
				Timeout:   30 * time.Second,
				RateLimit: 2 * time.Second,
				MaxPages:  10,
			},
			JavSP: javscraper.JavSPConfig{
				Enabled:   true,
				Sources:   []string{"javdb", "javlibrary", "avsox"},
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second,
			},
			Javinizer: javscraper.JavinizerConfig{
				Enabled:   true,
				Sources:   []string{"javlibrary", "r18", "dmm"},
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second,
			},
		},
	}

	javscraperManager, err := javscraper.NewManager(javscraperConfig)
	if err != nil {
		log.Fatalf("❌ 初始化JAV采集器失败: %v", err)
	}

	// 初始化统计
	stats := &CompleteScrapeStats{
		StartTime: time.Now(),
		Errors:    []string{},
	}

	fmt.Println("📋 开始JavBus完整分页采集...")
	fmt.Printf("⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Println("🔧 功能: 数据库写入 + 图片下载 + 自动下载触发")
	fmt.Println()

	// 测试aria2连接
	fmt.Println("🔌 测试aria2连接...")
	if err := aria2Service.Connect(); err != nil {
		fmt.Printf("⚠️  aria2连接失败: %v (将跳过自动下载)\n", err)
		aria2Service = nil
	} else {
		fmt.Println("✅ aria2连接成功")
	}

	// 执行完整采集
	err = performCompleteScraping(javscraperManager, javService, repo, aria2Service, stats)
	if err != nil {
		log.Fatalf("❌ 完整采集失败: %v", err)
	}

	// 显示最终统计
	showFinalStats(stats, telegramService)
}

func performCompleteScraping(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, stats *CompleteScrapeStats) error {
	javbusAPIURL := "http://localhost:3001/api/movies"
	currentPage := 1
	maxPages := 99999 // 无限制采集，直到没有更多页面

	fmt.Printf("🔍 开始从第1页采集，最大页数限制: %d\n", maxPages)
	fmt.Println()

	for currentPage <= maxPages {
		fmt.Printf("📄 正在采集第 %d 页...\n", currentPage)

		// 获取当前页的影片列表
		movies, pagination, err := fetchMoviesFromPage(javbusAPIURL, currentPage)
		if err != nil {
			errorMsg := fmt.Sprintf("获取第%d页失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
			
			if strings.Contains(err.Error(), "404") {
				fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage-1)
				break
			}
			
			currentPage++
			continue
		}

		stats.TotalPages = currentPage
		stats.TotalMovies += len(movies)

		fmt.Printf("  📊 找到 %d 部影片\n", len(movies))

		// 完整处理当前页的影片
		err = processMoviesPageComplete(scraperManager, javService, repo, aria2Service, movies, currentPage, stats)
		if err != nil {
			errorMsg := fmt.Sprintf("处理第%d页影片失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
		}

		// 智能停止条件检查
		if !pagination.HasNextPage {
			fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage)
			break
		}
		
		// 如果当前页没有影片，可能已到末尾
		if len(movies) == 0 {
			fmt.Printf("⚠️  第%d页没有找到影片，可能已到达末尾\n", currentPage)
			break
		}
		
		// 安全检查：如果页数过多，询问是否继续
		if currentPage >= 1000 && currentPage%100 == 0 {
			fmt.Printf("⚠️  已采集%d页，继续采集可能需要很长时间...\n", currentPage)
		}

		// 页面间休息
		fmt.Printf("  ⏳ 休息5秒后继续下一页...\n")
		time.Sleep(5 * time.Second)
		fmt.Println()

		currentPage++
	}

	stats.EndTime = time.Now()
	return nil
}

func fetchMoviesFromPage(baseURL string, page int) ([]JavBusMovie, *Pagination, error) {
	url := fmt.Sprintf("%s?page=%d", baseURL, page)
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil, fmt.Errorf("页面不存在 (404)")
	}

	if resp.StatusCode != 200 {
		return nil, nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var response JavBusMoviesResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return response.Movies, &response.Pagination, nil
}

func processMoviesPageComplete(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, movies []JavBusMovie, page int, stats *CompleteScrapeStats) error {
	if len(movies) == 0 {
		return nil
	}

	// 提取影片番号
	var codes []string
	for _, movie := range movies {
		if movie.ID != "" {
			codes = append(codes, strings.ToUpper(strings.TrimSpace(movie.ID)))
		}
	}

	if len(codes) == 0 {
		return fmt.Errorf("第%d页没有有效的影片番号", page)
	}

	fmt.Printf("  🔄 开始完整采集 %d 个影片番号...\n", len(codes))

	// 分批处理，每批3个（减少批次大小，确保质量）
	batchSize := 3
	for i := 0; i < len(codes); i += batchSize {
		end := i + batchSize
		if end > len(codes) {
			end = len(codes)
		}

		batch := codes[i:end]
		fmt.Printf("    📦 处理批次 %d-%d: %v\n", i+1, end, batch)

		// 完整采集每个影片
		for _, code := range batch {
			err := processSingleMovieComplete(scraperManager, javService, repo, aria2Service, code, stats)
			if err != nil {
				errorMsg := fmt.Sprintf("采集影片%s失败: %v", code, err)
				stats.Errors = append(stats.Errors, errorMsg)
				logger.Errorf(errorMsg)
				stats.FailedMovies++
			} else {
				stats.SuccessMovies++
			}
			stats.ProcessedMovies++

			// 影片间休息，避免过快请求
			time.Sleep(2 * time.Second)
		}

		fmt.Printf("    ✅ 批次完成: 成功%d, 失败%d\n", 
			min(len(batch), stats.SuccessMovies), 
			min(len(batch), stats.FailedMovies))

		// 批次间休息
		if end < len(codes) {
			time.Sleep(3 * time.Second)
		}
	}

	fmt.Printf("  ✅ 第%d页处理完成\n", page)
	return nil
}

func processSingleMovieComplete(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, code string, stats *CompleteScrapeStats) error {
	fmt.Printf("      🎬 采集影片: %s\n", code)

	// 1. 使用JAV采集器获取完整数据
	result, err := scraperManager.GetMovieByCode(code)
	if err != nil {
		return fmt.Errorf("采集器获取数据失败: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("采集失败: %s", result.Error)
	}

	fmt.Printf("        ✅ 数据采集成功 (来源: %s, 耗时: %v)\n", result.Source, result.Duration)

	// 2. 检查影片是否已存在
	existingMovie, err := javService.GetMovieByCode(code)
	isNewMovie := err != nil // 如果获取失败，说明是新影片

	if isNewMovie {
		stats.NewMovies++
		fmt.Printf("        🆕 新影片，将保存到数据库\n")
	} else {
		stats.UpdatedMovies++
		fmt.Printf("        🔄 更新现有影片 (ID: %d)\n", existingMovie.ID)
	}

	// 3. 保存影片数据到数据库
	err = saveMovieToDatabase(repo, result, stats, existingMovie, isNewMovie)
	if err != nil {
		return fmt.Errorf("保存数据库失败: %w", err)
	}
	
	// 4. 下载图片（封面、演员头像等）
	err = downloadAllImages(result.MovieInfo, code, stats)
	if err != nil {
		fmt.Printf("        ⚠️  图片下载失败: %v\n", err)
	}

	// 5. 如果有磁力链接，创建aria2下载任务
	if len(result.MovieInfo.Magnets) > 0 {
		fmt.Printf("        🧲 发现 %d 个磁力链接，智能筛选下载任务\n", len(result.MovieInfo.Magnets))
		
		// 使用智能磁力选择器
		selectedMagnets := selectBestMagnetsAdvanced(result.MovieInfo.Magnets, code)
		if len(selectedMagnets) > 0 {
			for i, magnet := range selectedMagnets {
				fmt.Printf("        📥 创建下载任务 %d/%d: %s\n", i+1, len(selectedMagnets), magnet.MagnetURL)
				fmt.Printf("           📊 评分: %.1f, 大小: %s, 字幕: %v\n", 
					magnet.Score, formatFileSize(magnet.FileSize), magnet.HasSubtitle)
				
				err = createAria2DownloadTask(aria2Service, magnet.MagnetInfo, code)
				if err != nil {
					fmt.Printf("        ⚠️  aria2下载失败: %v\n", err)
					// 如果aria2失败，保存到文件作为备用
					createDownloadTask(magnet.MagnetInfo, code)
					fmt.Printf("        💾 磁力链接已保存到文件\n")
				} else {
					stats.DownloadsCreated++
					fmt.Printf("        ✅ aria2下载任务创建成功\n")
				}
			}
		}
	}

	fmt.Printf("        ✅ 影片 %s 处理完成\n", code)
	return nil
}

// selectBestMagnetsAdvanced 智能选择磁力链接
// 策略：如果有字幕就优先下载字幕版本，没字幕就选最大的，如果两者都存在就都下载
func selectBestMagnetsAdvanced(magnets []javscraper.MagnetInfo, movieCode string) []*ScoredMagnet {
	if len(magnets) == 0 {
		return nil
	}

	fmt.Printf("        🔍 开始智能筛选磁力链接...\n")
	
	// 分析所有磁力链接
	var withSubtitle []*ScoredMagnet
	var withoutSubtitle []*ScoredMagnet
	
	for i := range magnets {
		// 创建带评分的磁力链接
		scoredMagnet := &ScoredMagnet{
			MagnetInfo: &magnets[i],
		}
		
		// 解析字幕信息
		parseSubtitleInfo(scoredMagnet)
		// 解析质量信息
		parseQualityInfo(scoredMagnet)
		// 计算评分
		scoredMagnet.Score = calculateMagnetScore(scoredMagnet)
		
		if scoredMagnet.HasSubtitle {
			withSubtitle = append(withSubtitle, scoredMagnet)
		} else {
			withoutSubtitle = append(withoutSubtitle, scoredMagnet)
		}
	}
	
	fmt.Printf("        📊 分析结果: %d个有字幕, %d个无字幕\n", len(withSubtitle), len(withoutSubtitle))
	
	var selectedMagnets []*ScoredMagnet
	
	// 策略1: 智能选择字幕版本
	if len(withSubtitle) > 0 {
		selectedSubtitles := selectBestSubtitleMagnets(withSubtitle)
		for i, magnet := range selectedSubtitles {
			selectedMagnets = append(selectedMagnets, magnet)
			fmt.Printf("        ✅ 选择字幕版本 %d: %s (评分: %.1f, 语言: %s)\n", 
				i+1, magnet.FileName, magnet.Score, magnet.SubtitleLanguage)
		}
	}
	
	// 策略2: 选择最大的无字幕版本（如果与字幕版本不同）
	if len(withoutSubtitle) > 0 {
		bestLarge := findBestMagnet(withoutSubtitle, "最大文件")
		if bestLarge != nil {
			// 检查是否与已选择的字幕版本重复
			isDuplicate := false
			for _, selected := range selectedMagnets {
				if isSimilarMagnet(selected, bestLarge) {
					isDuplicate = true
					break
				}
			}
			
			if !isDuplicate {
				selectedMagnets = append(selectedMagnets, bestLarge)
				fmt.Printf("        ✅ 选择最大文件: %s (评分: %.1f)\n", 
					bestLarge.FileName, bestLarge.Score)
			} else {
				fmt.Printf("        ⚠️  最大文件与字幕版本相似，跳过重复下载\n")
			}
		}
	}
	
	// 如果没有选择到任何磁力链接，选择评分最高的
	if len(selectedMagnets) == 0 && len(magnets) > 0 {
		allMagnets := make([]*ScoredMagnet, len(magnets))
		for i := range magnets {
			allMagnets[i] = &ScoredMagnet{MagnetInfo: &magnets[i]}
			parseSubtitleInfo(allMagnets[i])
			parseQualityInfo(allMagnets[i])
			allMagnets[i].Score = calculateMagnetScore(allMagnets[i])
		}
		fallback := findBestMagnet(allMagnets, "备选")
		if fallback != nil {
			selectedMagnets = append(selectedMagnets, fallback)
			fmt.Printf("        ✅ 备选方案: %s (评分: %.1f)\n", 
				fallback.FileName, fallback.Score)
		}
	}
	
	return selectedMagnets
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func showFinalStats(stats *CompleteScrapeStats, telegramService service.TelegramService) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println()
	fmt.Println("🎉 JavBus完整分页采集完成！")
	fmt.Println("=" + strings.Repeat("=", 70))
	fmt.Printf("📊 采集统计:\n")
	fmt.Printf("  总页数: %d\n", stats.TotalPages)
	fmt.Printf("  发现影片: %d 部\n", stats.TotalMovies)
	fmt.Printf("  处理影片: %d 部\n", stats.ProcessedMovies)
	fmt.Printf("  成功采集: %d 部\n", stats.SuccessMovies)
	fmt.Printf("  失败采集: %d 部\n", stats.FailedMovies)
	fmt.Printf("  新增影片: %d 部\n", stats.NewMovies)
	fmt.Printf("  更新影片: %d 部\n", stats.UpdatedMovies)
	fmt.Printf("  下载任务: %d 个\n", stats.DownloadsCreated)
	fmt.Printf("  下载图片: %d 张\n", stats.ImagesDownloaded)
	fmt.Printf("  成功率: %.1f%%\n", float64(stats.SuccessMovies)/float64(stats.ProcessedMovies)*100)
	fmt.Printf("  总耗时: %v\n", duration)
	if stats.TotalPages > 0 {
		fmt.Printf("  平均每页: %v\n", duration/time.Duration(stats.TotalPages))
	}
	if stats.ProcessedMovies > 0 {
		fmt.Printf("  平均每部: %v\n", duration/time.Duration(stats.ProcessedMovies))
	}

	if len(stats.Errors) > 0 {
		fmt.Printf("\n⚠️  错误信息 (%d个):\n", len(stats.Errors))
		for i, err := range stats.Errors {
			if i < 10 { // 只显示前10个错误
				fmt.Printf("  %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 10 {
			fmt.Printf("  ... 还有 %d 个错误\n", len(stats.Errors)-10)
		}
	}

	fmt.Printf("\n⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏰ 结束时间: %s\n", stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Println()
	fmt.Println("🎯 完整采集任务已完成！")
	fmt.Println("💾 数据已保存到数据库")
	fmt.Println("🖼️  图片已下载到本地")
	fmt.Println("📥 下载任务已创建，aria2将自动开始下载")

	// 发送Telegram通知
	if telegramService != nil && telegramService.IsEnabled() {
		fmt.Println("📱 发送Telegram通知...")
		
		// 计算成功率
		successRate := float64(0)
		if stats.ProcessedMovies > 0 {
			successRate = float64(stats.SuccessMovies) / float64(stats.ProcessedMovies) * 100
		}
		
		telegramStats := &telegram.ScrapingStats{
			TotalPages:       stats.TotalPages,
			TotalMovies:      stats.TotalMovies,
			ProcessedMovies:  stats.ProcessedMovies,
			SuccessMovies:    stats.SuccessMovies,
			FailedMovies:     stats.FailedMovies,
			NewMovies:        stats.NewMovies,
			UpdatedMovies:    stats.UpdatedMovies,
			DownloadsCreated: stats.DownloadsCreated,
			ImagesDownloaded: stats.ImagesDownloaded,
			StartTime:        stats.StartTime,
			EndTime:          stats.EndTime,
			Duration:         duration,
			SuccessRate:      successRate,
			ErrorCount:       len(stats.Errors),
		}
		
		err := telegramService.SendScrapingCompleted(telegramStats)
		if err != nil {
			fmt.Printf("⚠️  Telegram通知发送失败: %v\n", err)
		} else {
			fmt.Println("✅ Telegram通知发送成功")
		}
	}
}