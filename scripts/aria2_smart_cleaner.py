#!/usr/bin/env python3
"""
Aria2智能清理管理器
删除慢速任务的文件但保留任务记录，释放硬盘空间
"""

import json
import requests
import time
import logging
import os
import shutil
import psycopg2
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
DOWNLOAD_DIR = "/www/wwwroot/JAVAPI.COM/downloads"

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "postgres123",
    "database": "magnet_downloader"
}

# 性能阈值配置
MIN_SPEED_THRESHOLD = 50 * 1024  # 50KB/s 最低速度阈值
SLOW_TASK_TIMEOUT = 300  # 5分钟低速超时
MIN_FILE_SIZE_TO_CLEAN = 100 * 1024 * 1024  # 100MB以上才清理
MIN_DIR_SIZE_TO_KEEP = 100 * 1024 * 1024  # 100MB以下的目录会被删除
MAX_CONCURRENT_DOWNLOADS = 15

class Aria2SmartCleaner:
    def __init__(self):
        self.session = requests.Session()
        self.slow_tasks = {}  # 记录低速任务: {gid: start_time}
        self.cleaned_tasks = {}  # 记录已清理的任务: {gid: task_info}
        self.db_conn = None  # 数据库连接
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/www/wwwroot/JAVAPI.COM/logs/aria2_cleaner.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 创建状态文件目录
        self.state_dir = Path('/www/wwwroot/JAVAPI.COM/data/aria2_cleaner')
        self.state_dir.mkdir(parents=True, exist_ok=True)
        self.cleaned_tasks_file = self.state_dir / 'cleaned_tasks.json'
        
        # 加载已清理任务记录
        self.load_cleaned_tasks()
        
        # 初始化数据库连接
        self.init_database_connection()

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def init_database_connection(self):
        """初始化数据库连接"""
        try:
            self.db_conn = psycopg2.connect(**DB_CONFIG)
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            self.db_conn = None

    def get_uploaded_tasks(self) -> List[Dict]:
        """获取已上传的任务（有播放URL的任务）"""
        if not self.db_conn:
            return []
        
        try:
            cursor = self.db_conn.cursor()
            # 查询有播放URL且文件仍存在的任务
            query = """
                SELECT id, task_name, save_path, play_url, stream_tape_url, aria2_g_id
                FROM download_tasks 
                WHERE (play_url IS NOT NULL AND play_url != '') 
                   OR (stream_tape_url IS NOT NULL AND stream_tape_url != '')
                ORDER BY completed_at DESC
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            uploaded_tasks = []
            for row in results:
                task_id, task_name, save_path, play_url, stream_tape_url, aria2_g_id = row
                uploaded_tasks.append({
                    "id": task_id,
                    "name": task_name,
                    "save_path": save_path,
                    "play_url": play_url,
                    "stream_tape_url": stream_tape_url,
                    "aria2_gid": aria2_g_id
                })
            
            cursor.close()
            self.logger.info(f"查询到 {len(uploaded_tasks)} 个已上传的任务")
            return uploaded_tasks
            
        except Exception as e:
            self.logger.error(f"查询已上传任务失败: {e}")
            return []

    def get_active_downloads(self) -> List[Dict]:
        """获取活跃下载任务"""
        result = self.call_aria2("aria2.tellActive")
        if "result" in result:
            return result["result"]
        return []

    def get_stopped_downloads(self) -> List[Dict]:
        """获取已停止的下载任务"""
        result = self.call_aria2("aria2.tellStopped", [0, 100])
        if "result" in result:
            return result["result"]
        return []

    def pause_task(self, gid: str) -> bool:
        """暂停任务"""
        result = self.call_aria2("aria2.pause", [gid])
        return "result" in result

    def unpause_task(self, gid: str) -> bool:
        """恢复任务"""
        result = self.call_aria2("aria2.unpause", [gid])
        return "result" in result

    def get_task_info(self, task: Dict) -> Dict:
        """提取任务信息"""
        gid = task["gid"]
        download_speed = int(task.get("downloadSpeed", 0))
        completed_length = int(task.get("completedLength", 0))
        total_length = int(task.get("totalLength", 1))
        progress = (completed_length / max(total_length, 1)) * 100
        
        # 获取文件信息
        files = task.get("files", [])
        file_paths = []
        total_size = 0
        
        for file_info in files:
            file_path = file_info.get("path", "")
            file_size = int(file_info.get("length", 0))
            if file_path:
                file_paths.append(file_path)
                total_size += file_size
        
        task_name = self.extract_task_name(files)
        
        return {
            "gid": gid,
            "name": task_name,
            "speed": download_speed,
            "progress": progress,
            "completed_length": completed_length,
            "total_length": total_length,
            "file_paths": file_paths,
            "total_size": total_size,
            "status": task.get("status", "unknown")
        }

    def extract_task_name(self, files: List[Dict]) -> str:
        """提取任务名称"""
        if not files:
            return "Unknown"
        
        # 尝试从第一个文件路径提取名称
        first_file = files[0].get("path", "")
        if first_file:
            # 提取文件名或目录名
            path_parts = first_file.replace(DOWNLOAD_DIR, "").strip("/").split("/")
            if len(path_parts) > 1:
                return path_parts[0]  # 返回目录名
            else:
                return os.path.basename(first_file)  # 返回文件名
        
        return "Unknown"

    def calculate_disk_usage(self, file_paths: List[str]) -> int:
        """计算文件占用的磁盘空间"""
        total_size = 0
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
            except OSError:
                continue
        return total_size

    def clean_task_files(self, task_info: Dict) -> Dict:
        """清理任务文件但保留任务记录"""
        gid = task_info["gid"]
        file_paths = task_info["file_paths"]
        
        cleaned_size = 0
        cleaned_files = []
        failed_files = []
        
        for file_path in file_paths:
            try:
                # 修复路径：如果是相对路径，转换为绝对路径
                if file_path.startswith('/downloads/'):
                    actual_path = file_path.replace('/downloads/', DOWNLOAD_DIR + '/')
                else:
                    actual_path = file_path
                
                if os.path.exists(actual_path):
                    file_size = os.path.getsize(actual_path)
                    
                    # 只清理大于阈值的文件
                    if file_size >= MIN_FILE_SIZE_TO_CLEAN:
                        os.remove(actual_path)
                        cleaned_size += file_size
                        cleaned_files.append(actual_path)
                        self.logger.info(f"删除文件: {actual_path} ({file_size / (1024*1024):.1f} MB)")
                    else:
                        self.logger.debug(f"文件太小，跳过清理: {actual_path} ({file_size / (1024*1024):.1f} MB)")
                        
            except OSError as e:
                failed_files.append(f"{actual_path}: {e}")
                self.logger.error(f"删除文件失败: {actual_path} - {e}")
        
        # 尝试删除空目录
        self.cleanup_empty_directories(file_paths)
        
        return {
            "cleaned_size": cleaned_size,
            "cleaned_files": cleaned_files,
            "failed_files": failed_files
        }

    def clean_task_files_force(self, task_info: Dict) -> Dict:
        """强制清理任务文件（不受大小限制）- 专用于错误代码7"""
        gid = task_info["gid"]
        file_paths = task_info["file_paths"]
        
        cleaned_size = 0
        cleaned_files = []
        failed_files = []
        
        for file_path in file_paths:
            try:
                # 修复路径：如果是相对路径，转换为绝对路径
                if file_path.startswith('/downloads/'):
                    actual_path = file_path.replace('/downloads/', DOWNLOAD_DIR + '/')
                else:
                    actual_path = file_path
                
                if os.path.exists(actual_path):
                    file_size = os.path.getsize(actual_path)
                    
                    # 强制清理所有文件，不检查大小阈值
                    os.remove(actual_path)
                    cleaned_size += file_size
                    cleaned_files.append(actual_path)
                    self.logger.info(f"强制删除文件: {actual_path} ({file_size / (1024*1024):.1f} MB)")
                        
            except OSError as e:
                failed_files.append(f"{actual_path}: {e}")
                self.logger.error(f"强制删除文件失败: {actual_path} - {e}")
        
        # 尝试删除空目录
        self.cleanup_empty_directories(file_paths)
        
        return {
            "cleaned_size": cleaned_size,
            "cleaned_files": cleaned_files,
            "failed_files": failed_files
        }

    def cleanup_empty_directories(self, file_paths: List[str]):
        """清理空目录"""
        directories = set()
        for file_path in file_paths:
            directories.add(os.path.dirname(file_path))

        for directory in directories:
            try:
                if os.path.exists(directory) and not os.listdir(directory):
                    os.rmdir(directory)
                    self.logger.info(f"删除空目录: {directory}")
            except OSError:
                pass  # 目录不为空或其他错误，忽略

    def get_directory_size(self, directory_path: str) -> int:
        """计算目录的总大小（递归）"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue
        except (OSError, IOError):
            pass
        return total_size

    def clean_small_directories(self) -> Dict:
        """清理downloads目录下小于100MB的子目录"""
        cleaned_dirs = []
        cleaned_size = 0
        failed_dirs = []

        try:
            # 扫描downloads目录下的所有子目录
            for item in os.listdir(DOWNLOAD_DIR):
                item_path = os.path.join(DOWNLOAD_DIR, item)

                # 只处理目录
                if os.path.isdir(item_path):
                    dir_size = self.get_directory_size(item_path)

                    # 如果目录小于100MB，删除它
                    if dir_size < MIN_DIR_SIZE_TO_KEEP:
                        try:
                            import shutil
                            shutil.rmtree(item_path)
                            cleaned_dirs.append(item_path)
                            cleaned_size += dir_size
                            self.logger.info(f"删除小目录: {item_path} ({dir_size / (1024*1024):.1f} MB)")
                        except OSError as e:
                            failed_dirs.append(f"{item_path}: {e}")
                            self.logger.error(f"删除目录失败: {item_path} - {e}")
                    else:
                        self.logger.debug(f"目录大小超过阈值，保留: {item_path} ({dir_size / (1024*1024):.1f} MB)")

        except OSError as e:
            self.logger.error(f"扫描downloads目录失败: {e}")

        return {
            "cleaned_dirs": cleaned_dirs,
            "cleaned_size": cleaned_size,
            "failed_dirs": failed_dirs
        }

    def clean_uploaded_tasks(self) -> Dict:
        """清理已上传的任务文件"""
        uploaded_tasks = self.get_uploaded_tasks()
        if not uploaded_tasks:
            return {"cleaned_files": [], "cleaned_size": 0, "failed_files": []}
        
        cleaned_files = []
        cleaned_size = 0
        failed_files = []
        
        for task in uploaded_tasks:
            save_path = task["save_path"]
            if not save_path:
                continue
                
            # 检查文件是否存在
            if os.path.exists(save_path):
                try:
                    file_size = os.path.getsize(save_path)
                    
                    # 删除文件
                    os.remove(save_path)
                    cleaned_files.append(save_path)
                    cleaned_size += file_size
                    
                    self.logger.info(f"删除已上传文件: {save_path} ({file_size / (1024*1024):.1f} MB)")
                    
                    # 尝试删除空目录
                    dir_path = os.path.dirname(save_path)
                    if dir_path != DOWNLOAD_DIR:
                        try:
                            if os.path.exists(dir_path) and not os.listdir(dir_path):
                                os.rmdir(dir_path)
                                self.logger.info(f"删除空目录: {dir_path}")
                        except OSError:
                            pass
                    
                    # 如果有aria2 GID，尝试从aria2中移除任务
                    if task["aria2_gid"]:
                        self.remove_aria2_task(task["aria2_gid"])
                        
                except OSError as e:
                    failed_files.append(f"{save_path}: {e}")
                    self.logger.error(f"删除已上传文件失败: {save_path} - {e}")
        
        return {
            "cleaned_files": cleaned_files,
            "cleaned_size": cleaned_size,
            "failed_files": failed_files
        }

    def remove_aria2_task(self, gid: str) -> bool:
        """从aria2中移除任务"""
        try:
            # 尝试移除活跃任务
            result = self.call_aria2("aria2.remove", [gid])
            if "result" in result:
                self.logger.info(f"从aria2移除活跃任务: {gid}")
                return True
            
            # 尝试移除已停止的任务
            result = self.call_aria2("aria2.removeDownloadResult", [gid])
            if "result" in result:
                self.logger.info(f"从aria2移除已停止任务: {gid}")
                return True
                
        except Exception as e:
            self.logger.debug(f"移除aria2任务失败 {gid}: {e}")
        
        return False

    def process_slow_task(self, task_info: Dict) -> bool:
        """处理慢速任务：暂停并清理文件"""
        gid = task_info["gid"]
        
        # 1. 暂停任务
        if not self.pause_task(gid):
            self.logger.error(f"暂停任务失败: {task_info['name']} (GID: {gid})")
            return False
        
        self.logger.info(f"已暂停慢速任务: {task_info['name']} (速度: {task_info['speed']/1024:.1f} KB/s)")
        
        # 2. 清理文件
        clean_result = self.clean_task_files(task_info)
        
        # 3. 记录清理信息
        self.cleaned_tasks[gid] = {
            "task_info": task_info,
            "cleaned_at": datetime.now().isoformat(),
            "cleaned_size": clean_result["cleaned_size"],
            "cleaned_files": clean_result["cleaned_files"],
            "can_restart": True
        }
        
        # 4. 保存状态
        self.save_cleaned_tasks()
        
        self.logger.info(f"清理完成: {task_info['name']} - 释放空间 {clean_result['cleaned_size']/(1024*1024*1024):.2f} GB")
        
        return True

    def monitor_and_clean(self) -> Dict:
        """监控并清理慢速任务"""
        active_tasks = self.get_active_downloads()
        current_time = datetime.now()
        
        stats = {
            "total_active": len(active_tasks),
            "slow_tasks_detected": 0,
            "tasks_cleaned": 0,
            "space_freed": 0,
            "small_dirs_cleaned": 0,
            "small_dirs_space_freed": 0,
            "uploaded_files_cleaned": 0,
            "uploaded_files_space_freed": 0
        }
        
        for task in active_tasks:
            task_info = self.get_task_info(task)
            gid = task_info["gid"]
            speed = task_info["speed"]
            
            # 检查是否为慢速任务
            if speed < MIN_SPEED_THRESHOLD:
                if gid not in self.slow_tasks:
                    self.slow_tasks[gid] = current_time
                    self.logger.info(f"检测到慢速任务: {task_info['name']} (速度: {speed/1024:.1f} KB/s)")
                    stats["slow_tasks_detected"] += 1
                
                # 检查是否超时需要清理
                elif current_time - self.slow_tasks[gid] > timedelta(seconds=SLOW_TASK_TIMEOUT):
                    if self.process_slow_task(task_info):
                        stats["tasks_cleaned"] += 1
                        stats["space_freed"] += self.cleaned_tasks[gid]["cleaned_size"]
                    
                    # 从慢速任务记录中移除
                    del self.slow_tasks[gid]
            else:
                # 任务速度恢复，从慢速记录中移除
                if gid in self.slow_tasks:
                    del self.slow_tasks[gid]
        
        # 处理错误状态的任务（错误代码7：文件已存在）- 立即清理所有文件
        stopped_tasks = self.get_stopped_downloads()
        for task in stopped_tasks:
            if task.get("status") == "error" and task.get("errorCode") == "7":
                task_info = self.get_task_info(task)
                
                # 错误代码7立即清理，不检查文件大小阈值
                if task_info["completed_length"] > 0:  # 只要有下载内容就清理
                    self.logger.info(f"发现错误代码7任务，立即清理: {task_info['name']} (已下载: {task_info['completed_length']/(1024*1024):.1f}MB)")
                    
                    # 清理文件但不暂停任务（因为已经是错误状态）
                    clean_result = self.clean_task_files_force(task_info)  # 使用强制清理方法
                    
                    if clean_result["cleaned_size"] > 0:
                        # 记录清理信息
                        gid = task_info["gid"]
                        self.cleaned_tasks[gid] = {
                            "task_info": task_info,
                            "cleaned_at": datetime.now().isoformat(),
                            "cleaned_size": clean_result["cleaned_size"],
                            "cleaned_files": clean_result["cleaned_files"],
                            "can_restart": True,
                            "reason": "error_code_7_cleanup"
                        }
                        
                        stats["tasks_cleaned"] += 1
                        stats["space_freed"] += clean_result["cleaned_size"]
                        
                        self.logger.info(f"清理错误任务完成: {task_info['name']} - 释放空间 {clean_result['cleaned_size']/(1024*1024*1024):.2f} GB")
        
        # 清理小于100MB的目录
        small_dir_result = self.clean_small_directories()
        if small_dir_result["cleaned_size"] > 0:
            stats["small_dirs_cleaned"] = len(small_dir_result["cleaned_dirs"])
            stats["small_dirs_space_freed"] = small_dir_result["cleaned_size"]
            self.logger.info(f"清理小目录完成: 删除 {len(small_dir_result['cleaned_dirs'])} 个目录，释放空间 {small_dir_result['cleaned_size']/(1024*1024*1024):.2f} GB")
        else:
            stats["small_dirs_cleaned"] = 0
            stats["small_dirs_space_freed"] = 0
        
        # 清理已上传的任务文件
        uploaded_result = self.clean_uploaded_tasks()
        if uploaded_result["cleaned_size"] > 0:
            stats["uploaded_files_cleaned"] = len(uploaded_result["cleaned_files"])
            stats["uploaded_files_space_freed"] = uploaded_result["cleaned_size"]
            self.logger.info(f"清理已上传文件完成: 删除 {len(uploaded_result['cleaned_files'])} 个文件，释放空间 {uploaded_result['cleaned_size']/(1024*1024*1024):.2f} GB")
        else:
            stats["uploaded_files_cleaned"] = 0
            stats["uploaded_files_space_freed"] = 0

        # 保存清理状态
        if stats["tasks_cleaned"] > 0:
            self.save_cleaned_tasks()

        return stats

    def list_cleaned_tasks(self) -> List[Dict]:
        """列出已清理的任务"""
        return [
            {
                "gid": gid,
                "name": info["task_info"]["name"],
                "cleaned_at": info["cleaned_at"],
                "freed_space_gb": info["cleaned_size"] / (1024*1024*1024),
                "can_restart": info["can_restart"]
            }
            for gid, info in self.cleaned_tasks.items()
        ]

    def restart_cleaned_task(self, gid: str) -> bool:
        """重新启动已清理的任务"""
        if gid not in self.cleaned_tasks:
            self.logger.error(f"未找到已清理的任务: {gid}")
            return False
        
        task_info = self.cleaned_tasks[gid]["task_info"]
        
        # 尝试恢复任务
        if self.unpause_task(gid):
            self.logger.info(f"重新启动任务: {task_info['name']} (GID: {gid})")
            # 从已清理列表中移除
            del self.cleaned_tasks[gid]
            self.save_cleaned_tasks()
            return True
        else:
            self.logger.error(f"重新启动任务失败: {task_info['name']} (GID: {gid})")
            return False

    def save_cleaned_tasks(self):
        """保存已清理任务记录"""
        try:
            with open(self.cleaned_tasks_file, 'w', encoding='utf-8') as f:
                json.dump(self.cleaned_tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存清理记录失败: {e}")

    def load_cleaned_tasks(self):
        """加载已清理任务记录"""
        try:
            if self.cleaned_tasks_file.exists():
                with open(self.cleaned_tasks_file, 'r', encoding='utf-8') as f:
                    self.cleaned_tasks = json.load(f)
                self.logger.info(f"加载了 {len(self.cleaned_tasks)} 个已清理任务记录")
        except Exception as e:
            self.logger.error(f"加载清理记录失败: {e}")
            self.cleaned_tasks = {}

    def get_disk_usage_report(self) -> Dict:
        """获取磁盘使用报告"""
        try:
            statvfs = os.statvfs(DOWNLOAD_DIR)
            total_space = statvfs.f_frsize * statvfs.f_blocks
            free_space = statvfs.f_frsize * statvfs.f_bavail
            used_space = total_space - free_space
            
            return {
                "total_gb": total_space / (1024**3),
                "used_gb": used_space / (1024**3),
                "free_gb": free_space / (1024**3),
                "usage_percent": (used_space / total_space) * 100
            }
        except Exception as e:
            self.logger.error(f"获取磁盘使用情况失败: {e}")
            return {}

    def run_monitoring(self, interval: int = 120):
        """运行监控循环"""
        self.logger.info("Aria2智能清理器启动")
        
        while True:
            try:
                stats = self.monitor_and_clean()
                disk_info = self.get_disk_usage_report()
                
                if stats["tasks_cleaned"] > 0 or stats["small_dirs_cleaned"] > 0 or stats["uploaded_files_cleaned"] > 0:
                    log_msg = f"清理统计: 活跃任务 {stats['total_active']}, 检测慢速 {stats['slow_tasks_detected']}, 清理任务 {stats['tasks_cleaned']}, 释放空间 {stats['space_freed']/(1024*1024*1024):.2f} GB"
                    if stats["small_dirs_cleaned"] > 0:
                        log_msg += f", 清理小目录 {stats['small_dirs_cleaned']} 个, 释放空间 {stats['small_dirs_space_freed']/(1024*1024*1024):.2f} GB"
                    if stats["uploaded_files_cleaned"] > 0:
                        log_msg += f", 清理已上传文件 {stats['uploaded_files_cleaned']} 个, 释放空间 {stats['uploaded_files_space_freed']/(1024*1024*1024):.2f} GB"
                    self.logger.info(log_msg)
                
                if disk_info:
                    self.logger.info(
                        f"磁盘状态: 使用 {disk_info['used_gb']:.1f}GB / "
                        f"总计 {disk_info['total_gb']:.1f}GB "
                        f"({disk_info['usage_percent']:.1f}%)"
                    )
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，退出监控")
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)
    
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if self.db_conn:
            try:
                self.db_conn.close()
            except:
                pass


def main():
    import sys
    
    cleaner = Aria2SmartCleaner()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "monitor":
            # 持续监控模式
            cleaner.run_monitoring(interval=120)  # 每2分钟检查一次
            
        elif command == "once":
            # 单次清理
            stats = cleaner.monitor_and_clean()
            output_msg = f"清理完成: 清理了 {stats['tasks_cleaned']} 个任务，释放 {stats['space_freed']/(1024*1024*1024):.2f} GB"
            if stats.get('small_dirs_cleaned', 0) > 0:
                output_msg += f"，清理小目录 {stats['small_dirs_cleaned']} 个，释放 {stats['small_dirs_space_freed']/(1024*1024*1024):.2f} GB"
            if stats.get('uploaded_files_cleaned', 0) > 0:
                output_msg += f"，清理已上传文件 {stats['uploaded_files_cleaned']} 个，释放 {stats['uploaded_files_space_freed']/(1024*1024*1024):.2f} GB"
            print(output_msg)
            
        elif command == "list":
            # 列出已清理的任务
            cleaned = cleaner.list_cleaned_tasks()
            print(f"已清理任务 ({len(cleaned)} 个):")
            for task in cleaned:
                print(f"  - {task['name']} (GID: {task['gid']}) - 释放 {task['freed_space_gb']:.2f} GB")
                
        elif command == "restart" and len(sys.argv) > 2:
            # 重新启动指定任务
            gid = sys.argv[2]
            if cleaner.restart_cleaned_task(gid):
                print(f"任务 {gid} 重新启动成功")
            else:
                print(f"任务 {gid} 重新启动失败")
                
        elif command == "disk":
            # 显示磁盘使用情况
            disk_info = cleaner.get_disk_usage_report()
            if disk_info:
                print(f"磁盘使用情况:")
                print(f"  总空间: {disk_info['total_gb']:.1f} GB")
                print(f"  已使用: {disk_info['used_gb']:.1f} GB")
                print(f"  可用空间: {disk_info['free_gb']:.1f} GB")
                print(f"  使用率: {disk_info['usage_percent']:.1f}%")
        else:
            print("用法:")
            print("  python3 aria2_smart_cleaner.py monitor    # 持续监控模式")
            print("  python3 aria2_smart_cleaner.py once      # 单次清理")
            print("  python3 aria2_smart_cleaner.py list      # 列出已清理任务")
            print("  python3 aria2_smart_cleaner.py restart <GID>  # 重新启动任务")
            print("  python3 aria2_smart_cleaner.py disk      # 显示磁盘使用情况")
    else:
        # 默认单次清理
        stats = cleaner.monitor_and_clean()
        print(f"清理完成: 清理了 {stats['tasks_cleaned']} 个任务，释放 {stats['space_freed']/(1024*1024*1024):.2f} GB")


if __name__ == "__main__":
    main()